import { Client, Account, Databases, Storage, ID } from 'appwrite';

// Environment variables for Appwrite configuration
const APPWRITE_ENDPOINT = import.meta.env.VITE_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1';
const APPWRITE_PROJECT_ID = import.meta.env.VITE_APPWRITE_PROJECT_ID || '';

// Database and collection IDs from Phase 2 setup
export const DATABASE_ID = 'martyrs_db';
export const COLLECTIONS = {
  MARTYRS: 'martyrs',
  IMAGES: 'images',
  TIMELINE_EVENTS: 'timeline_events'
} as const;

// Storage bucket IDs from Phase 2 setup
export const STORAGE_BUCKETS = {
  MARTYR_IMAGES: 'martyr-images',
  MARTYR_DOCUMENTS: 'martyr-documents'
} as const;

// Initialize Appwrite client
export const client = new Client();

client
  .setEndpoint(APPWRITE_ENDPOINT)
  .setProject(APPWRITE_PROJECT_ID);

// Initialize Appwrite services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

// Export ID utility for generating unique IDs
export { ID };

// Type definitions for our data structures
export interface Martyr {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  slug: string;
  bio?: string;
  birth_date?: string;
  birth_place?: string;
  death_date?: string;
  death_place?: string;
  martyrdom_cause?: string;
  latitude?: number;
  longitude?: number;
  region?: string;
  period?: string;
  // Relationships
  images?: MartyrImage[];
  timeline_events?: TimelineEvent[];
}

export interface MartyrImage {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  url: string;
  caption?: string;
  credit?: string;
  is_profile_image: boolean;
  martyr: string; // Relationship ID
}

export interface TimelineEvent {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  title: string;
  description?: string;
  date: string;
  event_type?: string;
  martyr: string; // Relationship ID
}

// Authentication helper functions
export const auth = {
  // Login with email and password using object-based syntax
  async login(email: string, password: string) {
    try {
      const session = await account.createEmailPasswordSession({
        email,
        password
      });
      return session;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      return await account.get();
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  // Logout
  async logout() {
    try {
      await account.deleteSession({
        sessionId: 'current'
      });
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }
};

// Database helper functions
export const db = {
  // List documents with object-based syntax
  async listDocuments(collectionId: string, queries?: string[]) {
    try {
      return await databases.listDocuments({
        databaseId: DATABASE_ID,
        collectionId,
        queries: queries || []
      });
    } catch (error) {
      console.error('List documents error:', error);
      throw error;
    }
  },

  // Get single document
  async getDocument(collectionId: string, documentId: string) {
    try {
      return await databases.getDocument({
        databaseId: DATABASE_ID,
        collectionId,
        documentId
      });
    } catch (error) {
      console.error('Get document error:', error);
      throw error;
    }
  },

  // Create document
  async createDocument(collectionId: string, data: any, documentId?: string) {
    try {
      return await databases.createDocument({
        databaseId: DATABASE_ID,
        collectionId,
        documentId: documentId || ID.unique(),
        data
      });
    } catch (error) {
      console.error('Create document error:', error);
      throw error;
    }
  },

  // Update document
  async updateDocument(collectionId: string, documentId: string, data: any) {
    try {
      return await databases.updateDocument({
        databaseId: DATABASE_ID,
        collectionId,
        documentId,
        data
      });
    } catch (error) {
      console.error('Update document error:', error);
      throw error;
    }
  },

  // Delete document
  async deleteDocument(collectionId: string, documentId: string) {
    try {
      await databases.deleteDocument({
        databaseId: DATABASE_ID,
        collectionId,
        documentId
      });
    } catch (error) {
      console.error('Delete document error:', error);
      throw error;
    }
  }
};

// Storage helper functions
export const fileStorage = {
  // Upload file
  async uploadFile(bucketId: string, file: File, fileId?: string) {
    try {
      return await storage.createFile({
        bucketId,
        fileId: fileId || ID.unique(),
        file
      });
    } catch (error) {
      console.error('Upload file error:', error);
      throw error;
    }
  },

  // Get file preview URL
  getFilePreview(bucketId: string, fileId: string, width?: number, height?: number) {
    return storage.getFilePreview({
      bucketId,
      fileId,
      width,
      height
    });
  },

  // Get file download URL
  getFileDownload(bucketId: string, fileId: string) {
    return storage.getFileDownload({
      bucketId,
      fileId
    });
  },

  // Delete file
  async deleteFile(bucketId: string, fileId: string) {
    try {
      await storage.deleteFile({
        bucketId,
        fileId
      });
    } catch (error) {
      console.error('Delete file error:', error);
      throw error;
    }
  }
};

// Error handling utility
export const handleAppwriteError = (error: any) => {
  if (error?.code) {
    switch (error.code) {
      case 401:
        return 'Unauthorized. Please login again.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Resource already exists.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }
  return 'An unexpected error occurred.';
};

export default client;
