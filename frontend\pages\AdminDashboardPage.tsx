import React, { useState, useEffect, use<PERSON>emo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Plus, Search, Edit, Trash2, LogOut, Users, Eye, ChevronLeft, ChevronRight, CheckCircle, AlertCircle, TrendingUp, TrendingDown } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';
import { auth } from '../client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const AdminDashboardPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    // Check authentication using Appwrite session
    const checkAuth = async () => {
      try {
        const user = await auth.getCurrentUser();
        if (!user) {
          // No active session, redirect to login
          localStorage.removeItem('admin_user');
          navigate('/admin/login', { replace: true });
        }
      } catch (error: any) {
        console.error('Authentication check failed:', error);
        // Clear any local storage items related to auth
        localStorage.removeItem('admin_user');
        navigate('/admin/login', { replace: true });
      }
    };

    checkAuth();
  }, [navigate]);

  const getAuthenticatedBackend = () => {
    // With HttpOnly cookies, we don't need to manually set the Authorization header
    // The browser will automatically send cookies with requests
    return backend;
  };

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['admin-martyrs', page, searchQuery],
    queryFn: async () => {
      try {
        // Note: The backend now accepts query parameters for pagination and search
        // If the generated client doesn't support this yet, we'll use the original format
        return await getAuthenticatedBackend().martyrs.listMartyrs();
      } catch (error: any) {
        if (error?.message?.includes('unauthenticated')) {
          localStorage.removeItem('admin_user');
          navigate('/admin/login', { replace: true });
        }
        throw error;
      }
    },
  });

  const { data: viewsData, isLoading: isLoadingViews } = useQuery({
    queryKey: ['admin-views-over-time'],
    queryFn: async () => {
      try {
        const response = await getAuthenticatedBackend().martyrs.getViewsOverTime();
        return response.data.map(d => ({ ...d, name: new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) }));
      } catch (error) {
        console.error("Failed to fetch views data", error);
        return [];
      }
    },
  });

  const handleLogout = async () => {
    try {
      // Logout from Appwrite session
      await auth.logout();

      // Clear local storage
      localStorage.removeItem('admin_user');

      // Show toast
      toast({ title: "Logged Out Successfully" });

      // Navigate to login
      navigate('/admin/login', { replace: true });
    } catch (error) {
      console.error('Appwrite logout error:', error);
      // Even if Appwrite logout fails, clear local storage and redirect
      localStorage.removeItem('admin_user');
      toast({
        title: "Logged Out",
        description: "Session cleared locally.",
        variant: "default"
      });
      navigate('/admin/login', { replace: true });
    }
  };

  const handleDeleteMartyr = async (id: number) => {
    try {
      await getAuthenticatedBackend().martyrs.deleteMartyr({ id });
      toast({ title: "Martyr Deleted" });
      refetch();
    } catch (error) {
      toast({ title: "Delete Failed", variant: "destructive" });
    }
  };

  const martyrsByCategory = useMemo(() => {
    if (!data?.martyrs) return [];
    const categoryCount: Record<string, number> = {};
    data.martyrs.forEach((martyr) => {
      martyr.subCategories.forEach(cat => { 
        categoryCount[cat] = (categoryCount[cat] || 0) + 1; 
      });
    });
    return Object.entries(categoryCount).map(([name, value]) => ({ name, value }));
  }, [data]);

  const adminUser = JSON.parse(localStorage.getItem('admin_user') || '{}');
  const averageHealth = data?.averageContentHealth || 0;
  const profilesToFix = data?.martyrs.filter(m => m.contentHealth < 1).length || 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center"><span className="text-white font-bold text-sm">M</span></div>
              <h1 className="text-xl font-bold text-gray-900">Admin Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {adminUser.email || 'Admin'}</span>
              <Button variant="outline" size="sm" onClick={handleLogout}><LogOut className="w-4 h-4 mr-2" />Logout</Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">Total Martyrs</CardTitle><Users className="h-4 w-4 text-muted-foreground" /></CardHeader>
            <CardContent><div className="text-2xl font-bold">{data?.total || 0}</div><p className="text-xs text-muted-foreground flex items-center"><TrendingUp className="h-4 w-4 mr-1 text-green-500"/>+2 this month</p></CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">Total Views</CardTitle><Eye className="h-4 w-4 text-muted-foreground" /></CardHeader>
            <CardContent><div className="text-2xl font-bold">{data?.martyrs.reduce((s, m) => s + (m.viewCount || 0), 0) || 0}</div><p className="text-xs text-muted-foreground flex items-center"><TrendingUp className="h-4 w-4 mr-1 text-green-500"/>+12.5%</p></CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">Published</CardTitle><CheckCircle className="h-4 w-4 text-muted-foreground" /></CardHeader>
            <CardContent><div className="text-2xl font-bold">{data?.total || 0}</div><p className="text-xs text-muted-foreground">All profiles are live</p></CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">Content Health</CardTitle><AlertCircle className="h-4 w-4 text-muted-foreground" /></CardHeader>
            <CardContent><div className="text-2xl font-bold">{Math.round(averageHealth * 100)}%</div><p className="text-xs text-muted-foreground">{profilesToFix} profiles need attention</p></CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Views Over Time</CardTitle>
            </CardHeader>
            <CardContent className="h-96">
              {isLoadingViews ? (
                <div className="flex items-center justify-center h-full">Loading...</div>
              ) : (
                <Tabs defaultValue="line">
                  <TabsList>
                    <TabsTrigger value="line">Line</TabsTrigger>
                    <TabsTrigger value="bar">Bar</TabsTrigger>
                    <TabsTrigger value="area">Area</TabsTrigger>
                  </TabsList>
                  <TabsContent value="line" className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={viewsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="views" stroke="#16a34a" activeDot={{ r: 8 }} />
                      </LineChart>
                    </ResponsiveContainer>
                  </TabsContent>
                  <TabsContent value="bar" className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={viewsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="views" fill="#16a34a" />
                      </BarChart>
                    </ResponsiveContainer>
                  </TabsContent>
                  <TabsContent value="area" className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={viewsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Area type="monotone" dataKey="views" stroke="#16a34a" fill="#16a34a" fillOpacity={0.3} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
          <Card><CardHeader><CardTitle>Martyrs by Category</CardTitle></CardHeader><CardContent className="h-80"><ResponsiveContainer width="100%" height="100%"><PieChart><Pie data={martyrsByCategory} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} fill="#8884d8" label>{martyrsByCategory.map((entry, index) => <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />)}</Pie><Tooltip /></PieChart></ResponsiveContainer></CardContent></Card>
        </div>

        <Card>
          <CardHeader><div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"><div><CardTitle>Manage Martyrs</CardTitle><CardDescription>A list of all martyrs in the archive.</CardDescription></div><div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto"><div className="relative flex-1 sm:flex-initial"><Input type="text" placeholder="Search martyrs..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} className="pl-10 w-full sm:w-64" /><Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" /></div><Button onClick={() => navigate('/admin/martyrs/new')} className="bg-green-600 hover:bg-green-700"><Plus className="w-4 h-4 mr-2" />Add Martyr</Button></div></div></CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead><tr className="bg-gray-50 border-b"><th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Martyr</th><th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categories</th><th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th><th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Health</th><th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th></tr></thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {isLoading ? (<tr><td colSpan={5} className="text-center p-8">Loading...</td></tr>) : data?.martyrs.map((martyr) => (
                    <tr key={martyr.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap"><div className="flex items-center">{martyr.profileImage ? <img src={martyr.profileImage} alt={martyr.name} className="w-10 h-10 rounded-full object-cover" /> : <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"><span className="text-green-600 font-semibold">{martyr.name.charAt(0)}</span></div>}<div className="ml-4"><div className="text-sm font-medium text-gray-900">{martyr.name}</div><div className="text-sm text-gray-500">/{martyr.slug}</div></div></div></td>
                      <td className="px-6 py-4 whitespace-nowrap"><div className="flex flex-wrap gap-1">{martyr.subCategories.slice(0, 2).map(c => <Badge key={c} variant="secondary">{c}</Badge>)}{martyr.subCategories.length > 2 && <Badge variant="outline">+{martyr.subCategories.length - 2}</Badge>}</div></td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{martyr.region || '-'}</td>
                      <td className="px-6 py-4 whitespace-nowrap"><div className="flex items-center"><Progress value={martyr.contentHealth * 100} className="w-24" indicatorClassName={martyr.contentHealth < 0.5 ? 'bg-red-500' : martyr.contentHealth < 1 ? 'bg-yellow-500' : 'bg-green-500'} /> <span className="text-xs text-gray-500 ml-2">{Math.round(martyr.contentHealth * 100)}%</span></div></td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"><div className="flex items-center justify-end space-x-2"><Button variant="outline" size="sm" onClick={() => window.open(`/martyrs/${martyr.slug}`, '_blank')}><Eye className="w-4 h-4" /></Button><Button variant="outline" size="sm" onClick={() => navigate(`/admin/martyrs/${martyr.slug}/edit`)}><Edit className="w-4 h-4" /></Button><Button variant="outline" size="sm" onClick={() => handleDeleteMartyr(martyr.id)} className="text-red-600 hover:text-red-700"><Trash2 className="w-4 h-4" /></Button></div></td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="flex items-center justify-between pt-4"><div className="text-sm text-muted-foreground">Page {page} of {data?.pagination?.totalPages || 1}</div><div className="flex items-center space-x-2"><Button variant="outline" size="sm" onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1}><ChevronLeft className="h-4 w-4" /> Previous</Button><Button variant="outline" size="sm" onClick={() => setPage(p => p + 1)} disabled={!data?.pagination?.hasNext}>Next <ChevronRight className="h-4 w-4" /></Button></div></div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default AdminDashboardPage;