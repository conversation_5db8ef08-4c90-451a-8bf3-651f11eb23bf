{"name": "frontend", "version": "1.0.0", "type": "module", "packageManager": "npm@9.8.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@tailwindcss/vite": "^4.1.12", "appwrite": "^20.0.0", "@tanstack/react-query": "^5.85.3", "@tiptap/extension-link": "^3.3.0", "@tiptap/extension-text-align": "^3.3.0", "@tiptap/extension-underline": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "framer-motion": "^12.23.12", "lucide-react": "^0.484.0", "ol": "^8.2.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.8.1", "react-type-animation": "^3.2.0", "react-typed": "^2.0.12", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@tailwindcss/oxide": "^4.1.12", "@types/dompurify": "^3.0.5", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.7.0", "lightningcss": "^1.30.1", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "vite": "^6.3.5"}, "optionalDependencies": {"rollup": "^4.46.3"}}