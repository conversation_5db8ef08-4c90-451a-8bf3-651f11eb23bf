// Appwrite client configuration for Martyr Website
// Replaces the previous Encore.dev client

import { 
  client, 
  account, 
  databases, 
  storage, 
  auth, 
  db, 
  fileStorage,
  DATABASE_ID,
  COLLECTIONS,
  STORAGE_BUCKETS,
  handleAppwriteError,
  ID,
  type Martyr,
  type MartyrImage,
  type TimelineEvent
} from './lib/appwrite';

// Re-export Appwrite client and utilities
export { 
  account,
  databases,
  storage,
  auth,
  db,
  fileStorage,
  DATABASE_ID,
  COLLECTIONS,
  STORAGE_BUCKETS,
  handleAppwriteError,
  ID,
  type Martyr,
  type MartyrImage,
  type TimelineEvent
};

// Legacy compatibility types and functions for smooth migration
export type BaseURL = string;

export const Local: BaseURL = "appwrite-local";
export function Environment(name: string): BaseURL {
    return `appwrite-${name}`;
}
export function PreviewEnv(pr: number | string): BaseURL {
    return Environment(`pr${pr}`);
}

// Legacy Client class for compatibility
export class Client {
    public readonly auth: typeof auth;
    public readonly martyrs: any; // Will be implemented in subsequent tasks
    public readonly upload: typeof fileStorage;

    constructor(target?: BaseURL, options?: any) {
        // Initialize with Appwrite services
        this.auth = auth;
        this.upload = fileStorage;
        this.martyrs = {}; // Placeholder - will be implemented in Task 3.3
    }

    public with(options: any): Client {
        return new Client(undefined, options);
    }
}

// Error handling
export class APIError extends Error {
    public readonly status: number;
    public readonly code: string;
    public readonly details?: any;

    constructor(status: number, response: { code: string; message: string; details?: any }) {
        super(response.message);
        this.name = 'APIError';
        this.status = status;
        this.code = response.code;
        this.details = response.details;
    }
}

export function isAPIError(err: any): err is APIError {
    return err instanceof APIError;
}

// Default export - Appwrite client instance
export default client;
